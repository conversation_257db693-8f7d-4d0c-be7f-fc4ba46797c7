{"name": "ghazal-er<PERSON>i-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "figma:connect": "node scripts/create-figma-file.js", "figma:push": "node scripts/push-to-figma.js", "figma:setup": "echo 'Add your FIGMA_ACCESS_TOKEN to .env.local, then run npm run figma:connect'"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@types/d3": "^7.4.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "dotenv": "^17.0.1", "figma-api": "^2.0.2-beta", "framer-motion": "^11.15.0", "lucide-react": "^0.469.0", "next": "15.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}