# AYPortfolio Template

👋 Welcome! This is an open-source portfolio template designed to help developers, educators, and community builders showcase their unique journey and impact. Feel free to fork this repository and make it your own!

## ✨ Features

- 🎨 Modern, responsive design with dark mode
- 🔄 Interactive bubble visualization for role exploration
- 📱 Mobile-friendly and accessible
- 🎯 Focused sections for different aspects of your career
- ⚡️ Built with performance in mind

## 🛠 Tech Stack

- Next.js 15 with App Router
- TypeScript for type safety
- D3.js for interactive visualizations
- Tailwind CSS for styling
- shadcn/ui for component library
- Framer Motion for animations

## 🚀 Getting Started

1. Fork this repository
2. Clone your forked repository:
   ```bash
   git clone https://github.com/aysfl1/AYportfolio.git
   ```
3. Install dependencies:
   ```bash
   cd AYportfolio
   npm install
   ```
4. Start the development server:
   ```bash
   npm run dev
   ```
5. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📝 Customization

1. Update `src/config/portfolio-data.ts` with your personal information:
   - Add your experiences
   - Include your inspirations
   - Modify the persona descriptions

2. Replace images in the `public` directory:
   - Add your profile picture
   - Update inspiration images
   - Customize the meta image

3. Modify the theme in `tailwind.config.js`:
   - Change the color scheme
   - Adjust the typography
   - Update the animations

## 💡 Why This Template?

Traditional resumes and portfolios often fail to capture the multifaceted nature of modern tech careers. This template is designed to:

- Showcase different aspects of your professional identity
- Tell your story through meaningful connections
- Highlight your inspirations and their impact on your journey
- Present your experiences in an engaging, interactive way

## 🤝 Contributing

Found a bug or have an idea for improvement? Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is open source and available under the MIT License. Feel free to use it for your personal or professional portfolio!

## 🙏 Acknowledgments

Special thanks to:
- [shadcn/ui](https://ui.shadcn.com/) for the beautiful component library
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Next.js](https://nextjs.org/) for the awesome React framework

## 🌟 Show Your Portfolio!

Using this template? Add your portfolio to our showcase by submitting a pull request!
# AYportfolio

