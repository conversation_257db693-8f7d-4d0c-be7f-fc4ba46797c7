import React from 'react';
import FigmaIntegration from '@/components/FigmaIntegration';

export default function FigmaDemoPage() {
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
            Figma API Integration Demo
          </h1>
          
          <div className="mb-8">
            <FigmaIntegration />
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">Available Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-blue-50 p-4 rounded">
                <h3 className="font-semibold text-blue-800 mb-2">📁 File Access</h3>
                <p className="text-sm text-blue-700">
                  Fetch complete Figma file data including document structure, components, and metadata.
                </p>
              </div>
              
              <div className="bg-green-50 p-4 rounded">
                <h3 className="font-semibold text-green-800 mb-2">🔍 Node Search</h3>
                <p className="text-sm text-green-700">
                  Search for specific design elements by name across your entire Figma file.
                </p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded">
                <h3 className="font-semibold text-purple-800 mb-2">🖼️ Image Export</h3>
                <p className="text-sm text-purple-700">
                  Export any node as PNG, JPG, SVG, or PDF with customizable scale and quality.
                </p>
              </div>
              
              <div className="bg-orange-50 p-4 rounded">
                <h3 className="font-semibold text-orange-800 mb-2">🧩 Components</h3>
                <p className="text-sm text-orange-700">
                  Access all design components and their properties for design system integration.
                </p>
              </div>
            </div>
          </div>

          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-yellow-800 mb-3">🚀 Next Steps</h2>
            <ul className="list-disc list-inside text-sm text-yellow-700 space-y-2">
              <li>Set up your Figma Personal Access Token in the environment variables</li>
              <li>Add your Figma file key to start fetching design data</li>
              <li>Customize the integration component for your specific needs</li>
              <li>Build automated workflows to sync designs with your codebase</li>
              <li>Create design token extraction utilities</li>
              <li>Set up automated asset generation pipelines</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
