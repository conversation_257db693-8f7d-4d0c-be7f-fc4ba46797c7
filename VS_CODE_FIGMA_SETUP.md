# 🔗 VS Code Figma Integration Setup

## Your Design is Ready for Figma!

### Step 1: Open Figma Extension in VS Code
1. Press `Ctrl + Shift + P`
2. Type: `Figma: Login`
3. Sign in with: <EMAIL>

### Step 2: Create Figma File from Your Design
1. In VS Code, press `Ctrl + Shift + P`
2. Type: `Figma: Create new file`
3. Name it: "Ghazal Erfani Portfolio"

### Step 3: Import Your Design Components
Your design specification is ready in `figma-sync-spec.json`

**Components to create:**
- Main Portfolio Bubble (200x200px, gradient)
- Engineer Bubble (120x120px, #6366F1) ⚡
- Educator Bubble (120x120px, #8B5CF6) 📚  
- Movement Builder Bubble (120x120px, #EC4899) 🌟
- Inspiration Cards (300x200px, glass effect)

### Step 4: Enable Live Sync
1. In Figma, enable "Dev Mode"
2. In VS Code, use `Figma: Watch for changes`
3. Edit designs in Figma → See changes in VS Code!

### Your Current Design Colors:
- **accent1:** #6366F1 (Engineer)
- **accent2:** #8B5CF6 (Educator)  
- **accent3:** #EC4899 (Movement Builder)
- **Main gradient:** #3B82F6 → #60A5FA → #8B5CF6 → #EC4899

### Your Current Components:
- Interactive Bubble Chart with D3.js
- Persona-based sections
- Inspiration cards with glass effect
- Experience timeline
- Shimmer text animations

**Now you can edit your portfolio visually in Figma and sync changes back to VS Code!**
