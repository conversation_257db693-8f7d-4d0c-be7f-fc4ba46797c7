import type { Persona } from '@/types/portfolio';

export const portfolioData: Record<Persona['id'], Persona> = {
  'engineer': {
    id: 'engineer',
    title: '1',
    emoji: '💻',
    color: 'rgba(99, 102, 241, 1)',
    description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
    inspirations: [
      {
        name: '1',
        role: '@ @ @',
        image: '',
        lessons: [
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      },
      {
        name: '2',
        role: '@ @ @',
        image: '',
        lessons: [
         '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
         '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
         '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
         '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      },
      {
        name: '3',
        role: '@ @ @',
        image: '',
        lessons: [
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      },
      {
        name: '4',
        role: '@ @ @',
        image: '',
        lessons: [
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      }
    ],
    experiences: [
      {
        title: '1',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@', '@'],
        link: ''
      },
      {
        title: '2',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@', '@'],
        link: ''
      },
      {
        title: '3',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@', '@'],
        link: ''
      },
      {
        title: '4',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@', '@'],
        link: ''
      },
      {
        title: '5',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@', '@'],
        link: ''
      },
      {
        title: '6',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@', '@'],
        link: ''
      },
      {
        title: '7',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@', '@'],
        link: ''
      }
    ]
  },
  'educator': {
    id: 'educator',
    title: '2',
    emoji: '🤝',
    color: 'rgba(139, 92, 246, 1)',
    description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
    inspirations: [
      {
        name: '5',
        role: '@ @ @',
        image: '',
        lessons: [
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      },
      {
        name: '6',
        role: '@ @ @',
        image: '',
        lessons: [
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      },
      {
        name: '7',
        role: '@ @ @',
        image: '',
        lessons: [
              '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
              '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
              '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
              '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      }
    ],
    experiences: [
      {
        title: '8',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      },
      {
        title: '9',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      },
      {
        title: '10',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      },
      {
        title: '11',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      },
      {
        title: '12',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      }
    ]
  },
  'movement-builder': {
    id: 'movement-builder',
    title: '3',
    emoji: '🎨',
    color: 'rgba(236, 72, 153, 1)',
    description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
    inspirations: [
      {
        name: '8',
        role: '@ @ @',
        image: '',
        lessons: [
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
          '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      },
      {
        name: '9',
        role: '@ @ @',
        image: '',
        lessons: [
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      },
      {
        name: '10',
        role: '@ @ @',
        image: '',
        lessons: [
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      },
      {
        name: '11',
        role: '@ @ @',
        image: '',
        lessons: [
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @',
            '@ @ @ @ @ @ @ @ @ @ @ @ @ @'
        ]
      }
    ],
    experiences: [
      {
        title: '13',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      },
      {
        title: '14',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      },
      {
        title: '15',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      },
      {
        title: '16',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      },
      {
        title: '17',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      },
      {
        title: '18',
        description: '@ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @ @',
        date: '@ @ @',
        tags: ['@', '@', '@', '@'],
        link: ''
      }
    ]
  }
}; 
