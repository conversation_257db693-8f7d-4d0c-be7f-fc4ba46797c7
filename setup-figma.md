# 🎨 Quick Figma Setup for Gha<PERSON> Erfani

## Step 1: Get Your Figma Token (2 minutes)

1. **Open Figma Developer Settings**:
   - Go to: https://www.figma.com/developers/api#access-tokens
   - Sign in with: `<EMAIL>`

2. **Create Token**:
   - Click "Create new personal access token"
   - Name: `VS Code Portfolio Integration`
   - Click "Create token"
   - **Copy the token** (starts with `figd_`)

## Step 2: Add Token to VS Code

1. **Open `.env.local` file** (already exists in your project)

2. **Replace the placeholder**:
   ```env
   FIGMA_ACCESS_TOKEN=figd_your_actual_token_here
   ```
   
   Replace `figd_your_actual_token_here` with your actual token

## Step 3: Push to Figma

1. **Open VS Code Terminal** (Ctrl + `)

2. **Run the command**:
   ```bash
   npm run figma:push
   ```

3. **This will**:
   - Create a new Figma file named "Gha<PERSON> Erfani Portfolio"
   - Generate your portfolio design components
   - Give you the Figma file URL
   - Update your .env.local with the file key

## What Gets Created in Figma:

- 🎯 **Main Portfolio Bubble** with gradient
- ⚡ **Engineer Bubble** (blue)
- 📚 **Educator Bubble** (purple)  
- 🌟 **Movement Builder Bubble** (pink)
- 🎨 **Color Palette** with all your design tokens
- 📝 **Typography System**

## After Setup:

- Your Figma file will be at: `https://www.figma.com/file/YOUR_FILE_KEY/Ghazal-Erfani-Portfolio`
- You can edit designs in Figma
- Changes can be synced back to your VS Code
- Your portfolio website will be at: http://localhost:3000

## Need Help?

If you get any errors:
1. Make sure your Figma token is correct
2. Check that you're signed in to Figma
3. Restart VS Code after adding the token

**Ready? Run `npm run figma:push` in your terminal!** 🚀
